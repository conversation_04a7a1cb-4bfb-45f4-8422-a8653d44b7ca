import os
import django
import pandas as pd
import joblib

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "myproject.settings")
django.setup()

from inventory.models import Inventory, FactoryPart

# Build the dataset
data = []
mappings = FactoryPart.objects.select_related('factory', 'part')

for mapping in mappings:
    try:
        # Get current quantity from inventory or use 0 if not found
        inventory = Inventory.objects.get(factory=mapping.factory, part=mapping.part)
        current_quantity = inventory.current_quantity
    except Inventory.DoesNotExist:
        current_quantity = 0  # default if no inventory

    # Debugging print
    print(f"Factory ID: {mapping.factory.id}, Part ID: {mapping.part.id}")
    print(f"Current Quantity: {current_quantity}, Target Quantity: {mapping.target_quantity}")

    # Ensure we handle None or 0 for target_quantity
    target_quantity = mapping.target_quantity if mapping.target_quantity is not None else 0

    # Prepare the record
    record = {
        'factory_id': mapping.factory.id,
        'part_id': mapping.part.id,
        'current_quantity': current_quantity,
        'manufacturing_capacity_per_day': mapping.manufacturing_capacity_per_day,
        'lead_time_days': mapping.lead_time_days,
        'target_quantity': target_quantity
    }

    # New rule-based transfer_required label based on stock gap and manufacturing capacity
    stock_gap = target_quantity - current_quantity
    production_capacity = mapping.manufacturing_capacity_per_day * mapping.lead_time_days

    # Set transfer_required to 1 if the gap exceeds manufacturing capacity
    record['transfer_required'] = 1 if stock_gap > production_capacity else 0

    # Debugging print for stock gap and transfer_required decision
    print(f"Stock Gap: {stock_gap}, Transfer Required: {record['transfer_required']}")

    data.append(record)

# Create DataFrame and save to CSV (update existing CSV if it exists)
dataset_path = "inventory/ml_models/transfer_dataset.csv"
df = pd.DataFrame(data)

# If the file exists, append data to it, otherwise create a new file
if os.path.exists(dataset_path):
    df.to_csv(dataset_path, mode='a', header=False, index=False)
else:
    df.to_csv(dataset_path, index=False)

print("Dataset updated with new transfer_required values and saved to transfer_dataset.csv")
