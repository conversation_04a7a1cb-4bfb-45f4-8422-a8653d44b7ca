from django.urls import path
from . import views

urlpatterns = [
    path('register/', views.register, name='register'),
    path('dashboard/', views.dashboard_view, name='dashboard'),
    path('user-management/', views.user_management, name='user_management'),
    path('activity-log/', views.activity_log, name='activity_log'),

    # Audit Log URLs
    path('audit-logs/', views.audit_log_list, name='audit_log_list'),
    path('audit-logs/<int:audit_id>/', views.audit_log_detail, name='audit_log_detail'),
    path('audit-logs/create/', views.audit_log_create, name='audit_log_create'),
    path('audit-logs/<int:audit_id>/edit/', views.audit_log_edit, name='audit_log_edit'),
    path('audit-logs/<int:audit_id>/delete/', views.audit_log_delete, name='audit_log_delete'),

    # Factory URLs
    path('factories/', views.factory_list, name='factory_list'),
    path('factories/create/', views.factory_create, name='factory_create'),
    path('factories/<int:factory_id>/edit/', views.factory_update, name='factory_update'),
    path('factories/<int:factory_id>/delete/', views.factory_delete, name='factory_delete'),  # Fixed path

    # Part URLs
    path('parts/', views.part_list, name='part_list'),
    path('parts/create/', views.part_create, name='part_create'),
    path('parts/<int:part_id>/edit/', views.part_update, name='part_update'),
    path('parts/<int:part_id>/delete/', views.part_delete, name='part_delete'),

    # Factory-Part Mapping URLs
    path('factoryparts/', views.factory_part_list, name='factory_part_list'),
    path('factoryparts/create/', views.factory_part_create, name='factory_part_create'),
    path('factoryparts/<int:pk>/edit/', views.factory_part_update, name='factory_part_update'),
    path('factoryparts/<int:pk>/delete/', views.factory_part_delete, name='factory_part_delete'),

    # Inventory URLs
    path('inventory/', views.inventory_list, name='inventory_list'),
    path('inventory/create/', views.inventory_create, name='inventory_create'),
    path('inventory/<int:pk>/edit/', views.inventory_update, name='inventory_update'),
    path('inventory/<int:pk>/delete/', views.inventory_delete, name='inventory_delete'),

    # Production Log Dashboard
    path('production-log-dashboard/', views.production_log_dashboard, name='production_log_dashboard'),

    # Transfer Prediction (updated path to include dynamic factory_id and part_id)
    path('check-transfer/<int:factory_id>/<int:part_id>/', views.check_transfer, name='check_transfer'),

    # New URL pattern for selecting factory and part for transfer prediction
    path('select-factory-part-for-transfer/', views.select_factory_part_for_transfer, name='select_factory_part_for_transfer'),
]
