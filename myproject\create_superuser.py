import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from accounts.models import CustomUser

# Check if superuser already exists
if not CustomUser.objects.filter(username='admin').exists():
    # Create a superuser
    CustomUser.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        role='superuser'
    )
    print("Superuser created successfully!")
else:
    print("Superuser already exists.")
