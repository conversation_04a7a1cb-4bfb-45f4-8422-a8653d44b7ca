#!/usr/bin/env python3
"""
Test script for the enhanced ML prediction system
This script will:
1. Generate the enhanced dataset
2. Train the improved model
3. Test the prediction functionality
"""

import os
import sys
import django
import subprocess

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "myproject.settings")
django.setup()

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print("Output:")
                print(result.stdout)
        else:
            print("❌ ERROR")
            if result.stderr:
                print("Error output:")
                print(result.stderr)
            if result.stdout:
                print("Standard output:")
                print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False
    
    return True

def test_database_data():
    """Test if we have sufficient data in the database"""
    from inventory.models import Factory, Part, FactoryPart, Inventory
    
    print(f"\n{'='*60}")
    print("CHECKING DATABASE DATA")
    print(f"{'='*60}")
    
    factories = Factory.objects.count()
    parts = Part.objects.count()
    factory_parts = FactoryPart.objects.count()
    inventories = Inventory.objects.count()
    
    print(f"Factories: {factories}")
    print(f"Parts: {parts}")
    print(f"Factory-Part mappings: {factory_parts}")
    print(f"Inventory records: {inventories}")
    
    if factory_parts == 0:
        print("⚠️  WARNING: No factory-part mappings found. The system needs these for predictions.")
        return False
    
    print("✅ Database has sufficient data for testing")
    return True

def main():
    """Main test function"""
    print("🚀 TESTING ENHANCED ML PREDICTION SYSTEM")
    print("=" * 60)
    
    # Check database data
    if not test_database_data():
        print("❌ Database check failed. Please ensure you have factory-part mappings.")
        return
    
    # Step 1: Generate enhanced dataset
    success = run_command(
        "python generate_transfer_dataset.py",
        "Generating Enhanced Dataset"
    )
    if not success:
        print("❌ Dataset generation failed")
        return
    
    # Step 2: Train the improved model
    success = run_command(
        "python train_model.py",
        "Training Enhanced ML Model"
    )
    if not success:
        print("❌ Model training failed")
        return
    
    # Step 3: Check if model file was created
    model_path = "inventory/ml_models/transfer_model.pkl"
    if os.path.exists(model_path):
        print(f"✅ Model successfully saved to {model_path}")
    else:
        print(f"❌ Model file not found at {model_path}")
        return
    
    # Step 4: Test prediction functionality
    print(f"\n{'='*60}")
    print("TESTING PREDICTION FUNCTIONALITY")
    print(f"{'='*60}")
    
    try:
        from inventory.models import Factory, Part
        from inventory.views import predict_transfer
        
        # Get first factory-part combination for testing
        from inventory.models import FactoryPart
        factory_part = FactoryPart.objects.first()
        
        if factory_part:
            factory = factory_part.factory
            part = factory_part.part
            
            print(f"Testing prediction for:")
            print(f"  Factory: {factory.name}")
            print(f"  Part: {part.name}")
            
            prediction = predict_transfer(factory, part)
            prediction_text = "Yes" if prediction == 1 else "No"
            
            print(f"  Prediction: {prediction_text}")
            print("✅ Prediction functionality working")
        else:
            print("❌ No factory-part mappings found for testing")
            return
            
    except Exception as e:
        print(f"❌ Prediction test failed: {e}")
        return
    
    print(f"\n{'='*60}")
    print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    print(f"{'='*60}")
    print("\nYour enhanced ML prediction system is ready!")
    print("\nNext steps:")
    print("1. Start your Django server: python manage.py runserver")
    print("2. Navigate to the transfer prediction page")
    print("3. Test the enhanced interface with improved predictions")

if __name__ == "__main__":
    main()
