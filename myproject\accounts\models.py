from django.db import models
from django.contrib.auth.models import AbstractUser


class CustomUser(AbstractUser):
    """
    Custom User model with role-based permissions
    """
    ROLE_CHOICES = [
        ('superuser', 'Superuser'),
        ('manager', 'Factory Manager'),
        ('staff', 'Inventory Staff'),
    ]
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='staff')

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    @property
    def is_manager(self):
        return self.role == 'manager'
    
    @property
    def is_staff_member(self):
        return self.role == 'staff'
    
    @property
    def is_admin(self):
        return self.role == 'superuser' or self.is_superuser
