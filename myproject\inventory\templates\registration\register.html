<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register | LogistiQ</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .register-card {
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      background-color: white;
      width: 100%;
      max-width: 450px;
    }
    .register-title {
      font-weight: bold;
      font-size: 1.8rem;
      color: #003366;
    }
    .link-style {
      font-size: 0.9rem;
      color: #0056b3;
    }
    .success-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      animation: fadeIn 0.5s;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  </style>
</head>
<body>
  <!-- Success notification -->
  {% if messages %}
    {% for message in messages %}
      <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
        {{ message }}
      </div>
    {% endfor %}
  {% endif %}

  <div class="register-card">
    <h1 class="register-title text-center mb-4">Create an Account</h1>

    {% if form.non_field_errors %}
      <div class="alert alert-danger">
        {% for error in form.non_field_errors %}
          <p>{{ error }}</p>
        {% endfor %}
      </div>
    {% endif %}

    <form method="post">
      {% csrf_token %}

      <div class="mb-3">
        <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
        {{ form.username }}
        {% if form.username.errors %}
          <div class="text-danger">{{ form.username.errors }}</div>
        {% endif %}
      </div>

      <div class="mb-3">
        <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
        {{ form.password1 }}
        {% if form.password1.errors %}
          <div class="text-danger">{{ form.password1.errors }}</div>
        {% endif %}
        {% if show_password_requirements %}
          <div class="form-text">{{ form.password1.help_text }}</div>
        {% endif %}
      </div>

      <div class="mb-3">
        <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
        {{ form.password2 }}
        {% if form.password2.errors %}
          <div class="text-danger">{{ form.password2.errors }}</div>
        {% endif %}
      </div>

      <div class="mb-3">
        <label for="{{ form.role.id_for_label }}" class="form-label">Role</label>
        {{ form.role }}
        {% if form.role.errors %}
          <div class="text-danger">{{ form.role.errors }}</div>
        {% endif %}
        <div class="form-text">Note: Only one user per role is allowed.</div>
      </div>

      <button type="submit" class="btn btn-primary w-100 mb-3">Create Account</button>
    </form>

    <div class="text-center mt-3">
      <p>Already have an account? <a href="{% url 'login' %}" class="link-style">Log in</a></p>
    </div>
  </div>

  <!-- Bootstrap JS for notifications auto-close -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Auto-close notifications after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        var alerts = document.querySelectorAll('.success-notification');
        alerts.forEach(function(alert) {
          alert.style.opacity = '0';
          setTimeout(function() {
            alert.style.display = 'none';
          }, 500);
        });
      }, 5000);
    });
  </script>
</body>
</html>
