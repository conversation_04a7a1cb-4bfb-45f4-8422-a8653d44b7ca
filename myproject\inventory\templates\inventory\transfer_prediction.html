<!-- transfer_prediction.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Prediction</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f0f8ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .back-button {
            background-color: white;
            color: #007bff;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            border: 2px solid white;
            transition: 0.3s;
        }
        .back-button:hover {
            background-color: #e6f0ff;
            color: #0056b3;
            border-color: #e6f0ff;
        }
        .container {
            max-width: 700px;
            margin: 60px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.1);
        }
        h2 {
            text-align: center;
            color: #007bff;
            margin-bottom: 30px;
        }
        p {
            font-size: 18px;
            margin: 10px 0;
        }
        strong {
            color: #0056b3;
        }
        h3 {
            text-align: center;
            margin-top: 30px;
            color: #333;
        }
    </style>
</head>
<body>
    <header>
        <h1>Transfer Prediction</h1>
        <a href="javascript:history.back()" class="back-button">Back</a>
    </header>

    <div class="container">
        <h2>Prediction for Part: {{ part.name }}</h2>

        <div>
            <p><strong>Factory:</strong> {{ factory.name }}</p>
            <p><strong>Part Name:</strong> {{ part.name }}</p>
            <p><strong>Current Stock:</strong> {{ inventory.current_quantity }}</p>
        </div>

        {% if prediction %}
            <h3>Transfer Needed: <strong>{{ prediction }}</strong></h3>
        {% endif %}
    </div>
</body>
</html>
