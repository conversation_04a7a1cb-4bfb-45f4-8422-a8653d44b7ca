<!-- templates/registration/login.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login | LogistiQ</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .login-card {
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
      background-color: white;
      width: 100%;
      max-width: 350px;
    }
    .login-title {
      font-weight: bold;
      font-size: 2rem;
      color: #003366;
    }
    .link-style {
      font-size: 0.9rem;
      color: #0056b3;
    }
    .success-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      animation: fadeIn 0.5s;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  </style>
</head>
<body>
  <!-- Success notification -->
  {% if messages %}
    {% for message in messages %}
      <div class="alert alert-{{ message.tags }} success-notification">
        {{ message }}
      </div>
    {% endfor %}
  {% endif %}

  <div class="login-card text-center">
    <h1 class="login-title">LogistiQ</h1>
    <p class="text-muted">Inventory Management System</p>

    {% if form.errors %}
      <div class="alert alert-danger" role="alert">
        Invalid username or password.
      </div>
    {% endif %}

    <form method="post" action="{% url 'login' %}">
      {% csrf_token %}
      <div class="mb-3">
        <input type="text" name="username" class="form-control" placeholder="Username" required autofocus>
      </div>
      <div class="mb-3">
        <input type="password" name="password" class="form-control" placeholder="Password" required>
      </div>
      <div class="form-check mb-3 text-start">
        <input type="checkbox" class="form-check-input" id="remember" name="remember">
        <label class="form-check-label" for="remember">Remember me</label>
      </div>
      <button type="submit" class="btn btn-primary w-100 mb-3">Log In</button>

      <!-- Add a hidden field to store the next URL -->
      <input type="hidden" name="next" value="{{ next }}">
    </form>

    <div class="mt-3">
      <a href="#" class="link-style">Forgot password?</a><br>
      <a href="{% url 'register' %}" class="link-style">Don't have an account? Register</a>
    </div>
  </div>

  <!-- Bootstrap JS for notifications auto-close -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Auto-close notifications after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        var alerts = document.querySelectorAll('.success-notification');
        alerts.forEach(function(alert) {
          alert.style.opacity = '0';
          setTimeout(function() {
            alert.style.display = 'none';
          }, 500);
        });
      }, 5000);
    });
  </script>
</body>
</html>
