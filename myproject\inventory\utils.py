import os
from django.conf import settings
from inventory.models import Inventory, FactoryPart

# Try to import required packages and load the model
try:
    import joblib
    import pandas as pd

    # Load the model
    MODEL_PATH = os.path.join(settings.BASE_DIR, 'inventory', 'ml_models', 'transfer_model.pkl')
    model = joblib.load(MODEL_PATH)
    ML_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Machine learning dependencies not available: {e}")
    print("Please install: pip install joblib scikit-learn pandas")
    model = None
    ML_AVAILABLE = False
except Exception as e:
    print(f"Warning: Could not load ML model: {e}")
    model = None
    ML_AVAILABLE = False

def predict_transfer(factory, part):
    # Check if ML dependencies are available
    if not ML_AVAILABLE:
        return "ML prediction unavailable. Please install required packages: pip install joblib scikit-learn pandas"

    try:
        inventory = Inventory.objects.get(factory=factory, part=part)
        fp = FactoryPart.objects.get(factory=factory, part=part)
        supplier_links = FactoryPart.objects.filter(part=part).exclude(factory=factory)

        # Extracting features
        current_stock = inventory.current_quantity
        target_quantity = fp.target_quantity
        stock_deficit = max(0, target_quantity - current_stock)
        supplier_count = supplier_links.count()
        lead_times = [s.lead_time_days for s in supplier_links if s.lead_time_days is not None]
        capacities = [s.manufacturing_capacity_per_day for s in supplier_links if s.manufacturing_capacity_per_day is not None]

        fastest_lead_time = min(lead_times) if lead_times else 0
        max_supplier_capacity = max(capacities) if capacities else 0

        features = pd.DataFrame([{
            'current_stock': current_stock,
            'target_quantity': target_quantity,
            'stock_deficit': stock_deficit,
            'supplier_count': supplier_count,
            'fastest_lead_time': fastest_lead_time,
            'max_supplier_capacity': max_supplier_capacity,
        }])

        prediction = model.predict(features)[0]
        return prediction

    except Inventory.DoesNotExist:
        return "Error: Inventory record not found."
    except FactoryPart.DoesNotExist:
        return "Error: FactoryPart mapping not found."
    except Exception as e:
        return f"Unexpected error: {e}"
