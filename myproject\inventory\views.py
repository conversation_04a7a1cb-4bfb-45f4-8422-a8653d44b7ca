from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.contrib.auth import login, authenticate, get_user_model
from django.db.models import Sum, Q
from django.db.models.functions import TruncDate
from datetime import datetime, timedelta
from .models import Factory, Part, FactoryPart, Inventory, ProductionLog, ActivityLog, AuditLog
from .forms import FactoryForm, PartForm, FactoryPartForm, InventoryForm, SimpleUserCreationForm, AuditLogForm
from inventory.utils import predict_transfer
from django.http import HttpResponse
import joblib
import os
from django.conf import settings

# Get the user model
User = get_user_model()

model_path = os.path.join(settings.BASE_DIR, 'inventory/ml_models/transfer_model.pkl')
transfer_model = joblib.load(model_path)

# Role-based access control functions
def is_admin(user):
    return user.is_authenticated and (
        (hasattr(user, 'role') and user.role == 'superuser') or
        user.is_superuser
    )

def is_manager(user):
    return user.is_authenticated and (
        (hasattr(user, 'role') and user.role == 'manager') or
        is_admin(user)  # Admins can do everything managers can
    )

def is_employee(user):
    return user.is_authenticated  # All authenticated users have at least employee access

# Helper function to get role display name
def get_role_display(user):
    if is_admin(user):
        return "Admin"
    elif is_manager(user):
        return "Manager"
    else:
        return "Employee"

def register(request):
    """
    User registration view with simplified password requirements and automatic login
    """
    # Clear any existing messages to ensure we only show registration-related messages
    storage = messages.get_messages(request)
    storage.used = True

    if request.method == 'POST':
        form = SimpleUserCreationForm(request.POST)
        try:
            if form.is_valid():
                # Save the user
                user = form.save()
                username = form.cleaned_data.get('username')
                password = form.cleaned_data.get('password1')

                # Force login the user directly without authenticate
                user.backend = 'django.contrib.auth.backends.ModelBackend'
                login(request, user)

                # Add a clear success message only on successful registration
                messages.success(request, f'Welcome, {username}! Your account has been created successfully.')

                # Redirect to dashboard
                return redirect('dashboard')
            else:
                # Only print errors to console, don't show to user unless necessary
                print("Form errors:", form.errors)

                # Only show critical errors to the user
                if 'username' in form.errors:
                    messages.error(request, "Username is already taken. Please choose another one.")
                elif 'password2' in form.errors:
                    messages.error(request, "Passwords don't match. Please try again.")
        except Exception as e:
            # Log any exceptions but don't show technical details to user
            print(f"Exception during registration: {str(e)}")
            messages.error(request, "An error occurred during registration. Please try again.")
    else:
        form = SimpleUserCreationForm()

    return render(request, 'registration/register.html', {
        'form': form,
        'show_password_requirements': False  # Hide password requirements
    })

# Dashboard View
@login_required
def dashboard_view(request):
    user = request.user

    # Determine which dashboard to show based on user role
    if hasattr(user, 'role'):
        if user.role == 'superuser' or user.is_superuser:
            template = 'inventory/admin_dashboard.html'
        elif user.role == 'manager':
            template = 'inventory/manager_dashboard.html'
        else:  # Default to employee dashboard for any other role
            template = 'inventory/employee_dashboard.html'
    else:
        # Fallback to employee dashboard if role is not set
        template = 'inventory/employee_dashboard.html'

    context = {
        'user': user,
        'factories_count': Factory.objects.count(),
        'parts_count': Part.objects.count(),
        'recent_inventory': Inventory.objects.order_by('-updated_at')[:5],
        'production_stats': ProductionLog.objects.aggregate(
            total_produced=Sum('quantity_produced')
        )
    }

    return render(request, template, context)

# Factory Views
@login_required
def factory_list(request):
    query = request.GET.get('q')
    factories = Factory.objects.all()

    if query:
        factories = factories.filter(
            Q(name__icontains=query) | Q(location__icontains=query)
        )

    return render(request, 'inventory/factory_list.html', {
        'factories': factories,
        'search_query': query
    })

@login_required
def factory_create(request):
    if request.method == 'POST':
        form = FactoryForm(request.POST)
        if form.is_valid():
            factory = form.save()
            messages.success(request, f'Factory "{factory.name}" added successfully!')
            return redirect('factory_list')
    else:
        form = FactoryForm()

    return render(request, 'inventory/factory_form.html', {'form': form})

@login_required
def factory_update(request, factory_id):
    factory = get_object_or_404(Factory, id=factory_id)

    if request.method == 'POST':
        form = FactoryForm(request.POST, instance=factory)
        if form.is_valid():
            form.save()
            messages.success(request, f'Factory "{factory.name}" updated successfully!')
            return redirect('factory_list')
    else:
        form = FactoryForm(instance=factory)

    return render(request, 'inventory/factory_form.html', {
        'form': form,
        'factory': factory
    })

@login_required
@user_passes_test(is_manager, login_url='factory_list')
def factory_delete(request, factory_id):
    factory = get_object_or_404(Factory, id=factory_id)

    if request.method == 'POST':
        factory_name = factory.name
        factory.delete()
        messages.success(request, f'Factory "{factory_name}" deleted successfully!')
        return redirect('factory_list')

    return render(request, 'inventory/factory_confirm_delete.html', {
        'factory': factory
    })

# Part Views
@login_required
def part_list(request):
    parts = Part.objects.all()
    return render(request, 'inventory/part_list.html', {'parts': parts})

@login_required
def part_create(request):
    if request.method == 'POST':
        form = PartForm(request.POST)
        if form.is_valid():
            part = form.save()
            messages.success(request, f'Part "{part.name}" added successfully!')
            return redirect('part_list')
    else:
        form = PartForm()

    return render(request, 'inventory/part_form.html', {'form': form})

@login_required
def part_update(request, part_id):
    part = get_object_or_404(Part, id=part_id)

    if request.method == 'POST':
        form = PartForm(request.POST, instance=part)
        if form.is_valid():
            form.save()
            messages.success(request, f'Part "{part.name}" updated successfully!')
            return redirect('part_list')
    else:
        form = PartForm(instance=part)

    return render(request, 'inventory/part_form.html', {
        'form': form,
        'part': part
    })

@login_required
def part_delete(request, part_id):
    part = get_object_or_404(Part, id=part_id)

    if request.method == 'POST':
        part_name = part.name
        part.delete()
        messages.success(request, f'Part "{part_name}" deleted successfully!')
        return redirect('part_list')

    return render(request, 'inventory/part_confirm_delete.html', {
        'part': part
    })

# Factory-Part Mapping Views
@login_required
def factory_part_list(request):
    mappings = FactoryPart.objects.select_related('factory', 'part').all()
    return render(request, 'inventory/factorypart_list.html', {
        'mappings': mappings
    })

@login_required
def factory_part_create(request):
    if request.method == 'POST':
        form = FactoryPartForm(request.POST)
        if form.is_valid():
            mapping = form.save()
            messages.success(request, 'Factory-Part mapping created successfully!')
            return redirect('factory_part_list')
    else:
        form = FactoryPartForm()

    return render(request, 'inventory/factorypart_form.html', {'form': form})

@login_required
def factory_part_update(request, pk):
    mapping = get_object_or_404(FactoryPart, pk=pk)

    if request.method == 'POST':
        form = FactoryPartForm(request.POST, instance=mapping)
        if form.is_valid():
            form.save()
            messages.success(request, 'Mapping updated successfully!')
            return redirect('factory_part_list')
    else:
        form = FactoryPartForm(instance=mapping)

    return render(request, 'inventory/factorypart_form.html', {
        'form': form,
        'mapping': mapping
    })

@login_required
def factory_part_delete(request, pk):
    mapping = get_object_or_404(FactoryPart, pk=pk)

    if request.method == 'POST':
        mapping.delete()
        messages.success(request, 'Mapping deleted successfully!')
        return redirect('factory_part_list')

    return render(request, 'inventory/factorypart_confirm_delete.html', {
        'mapping': mapping
    })

# Inventory Views
@login_required
def inventory_list(request):
    inventory = Inventory.objects.select_related(
        'factory',
        'part'
    ).order_by('-updated_at')

    return render(request, 'inventory/inventory_list.html', {
        'inventory': inventory
    })

@login_required
def inventory_create(request):
    if request.method == 'POST':
        form = InventoryForm(request.POST)
        if form.is_valid():
            inventory = form.save()
            messages.success(request, 'Inventory record added successfully!')
            return redirect('inventory_list')
    else:
        form = InventoryForm()

    return render(request, 'inventory/inventory_form.html', {'form': form})

@login_required
def inventory_update(request, pk):
    item = get_object_or_404(Inventory, pk=pk)

    if request.method == 'POST':
        form = InventoryForm(request.POST, instance=item)
        if form.is_valid():
            form.save()
            messages.success(request, 'Inventory updated successfully!')
            return redirect('inventory_list')
    else:
        form = InventoryForm(instance=item)

    return render(request, 'inventory/inventory_form.html', {
        'form': form,
        'item': item
    })

@login_required
def inventory_delete(request, pk):
    item = get_object_or_404(Inventory, pk=pk)

    if request.method == 'POST':
        item.delete()
        messages.success(request, 'Inventory record deleted successfully!')
        return redirect('inventory_list')

    return render(request, 'inventory/inventory_confirm_delete.html', {
        'item': item
    })

# Production Log Dashboard View - Restricted to managers and admins
@login_required
@user_passes_test(is_manager, login_url='dashboard')
def production_log_dashboard(request):
    try:
        start_date = request.GET.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        end_date = request.GET.get('end_date', datetime.now().strftime('%Y-%m-%d'))

        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        messages.error(request, 'Invalid date format. Using default date range.')
        start_date = (datetime.now() - timedelta(days=30)).date()
        end_date = datetime.now().date()

    inventory_data = (
        Inventory.objects
        .filter(updated_at__date__range=[start_date, end_date])
        .annotate(day=TruncDate('updated_at'))
        .values('day')
        .annotate(total_quantity=Sum('current_quantity'))
        .order_by('day')
    )

    return render(request, 'inventory/production_log_dashboard.html', {
        'labels': [entry['day'].strftime('%Y-%m-%d') for entry in inventory_data],
        'values': [entry['total_quantity'] or 0 for entry in inventory_data],
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
    })
#Transfer start
model_path = os.path.join(os.getcwd(), 'inventory', 'ml_models', 'transfer_model.pkl')

try:
    transfer_model = joblib.load(model_path)
except FileNotFoundError:
    transfer_model = None
    print(f"Model not found at {model_path}")

# Transfer Prediction View - Restricted to admins only
@login_required
@user_passes_test(is_admin, login_url='dashboard')
def check_transfer(request, factory_id, part_id):
    factory = get_object_or_404(Factory, id=factory_id)
    part = get_object_or_404(Part, id=part_id)

    # Get the prediction from the utility function
    prediction = predict_transfer(factory, part)
    prediction_text = "Yes" if prediction == 1 else "No"

    return render(request, 'transfer_prediction.html', {
        'factory': factory,
        'part': part,
        'prediction': prediction_text,
    })

# New view to handle the selection of a factory and part for transfer prediction - Restricted to admins only
@login_required
@user_passes_test(is_admin, login_url='dashboard')
def select_factory_part_for_transfer(request):
    # Only show factories that have existing transfers (FactoryPart mappings)
    factories_with_transfers = Factory.objects.filter(
        factorypart__isnull=False
    ).distinct().order_by('name')

    # Only show parts that have existing transfers (FactoryPart mappings)
    parts_with_transfers = Part.objects.filter(
        factorypart__isnull=False
    ).distinct().order_by('name')

    if request.method == 'POST':
        factory_id = request.POST.get('factory')
        part_id = request.POST.get('part')

        factory = get_object_or_404(Factory, id=factory_id)
        part = get_object_or_404(Part, id=part_id)

        # Check if this factory-part combination has a mapping
        factory_part = FactoryPart.objects.filter(factory=factory, part=part).first()
        if not factory_part:
            messages.error(request, f"No transfer mapping exists between {factory.name} and {part.name}")
            return render(request, 'inventory/select_factory_part_for_transfer.html', {
                'factories': factories_with_transfers,
                'parts': parts_with_transfers,
                'error_message': f"No transfer mapping exists between {factory.name} and {part.name}"
            })

        # Get inventory data
        inventory = Inventory.objects.filter(factory=factory, part=part).first()

        # Find potential source factories for transfer
        potential_sources = FactoryPart.objects.filter(
            part=part
        ).exclude(factory=factory).select_related('factory')

        # Predict the transfer need
        prediction = predict_transfer(factory, part)
        prediction_text = "Yes" if prediction == 1 else "No"

        return render(request, 'inventory/transfer_prediction.html', {
            'factory': factory,
            'part': part,
            'inventory': inventory,
            'factory_part': factory_part,
            'potential_sources': potential_sources,
            'prediction': prediction_text,
        })

    return render(request, 'inventory/select_factory_part_for_transfer.html', {
        'factories': factories_with_transfers,
        'parts': parts_with_transfers,
    })

# Utility function to make transfer prediction
def predict_transfer(factory, part):
    if transfer_model is None:
        print("Model is not loaded.")
        return 0  # Default prediction if model is not loaded

    inventory = Inventory.objects.filter(part=part, factory=factory).first()
    factory_part = FactoryPart.objects.filter(factory=factory, part=part).first()

    if inventory and factory_part:
        try:
            # Basic features
            current_quantity = inventory.current_quantity
            manufacturing_capacity_per_day = factory_part.manufacturing_capacity_per_day
            lead_time_days = factory_part.lead_time_days
            target_quantity = factory_part.target_quantity

            # Engineered features (same as in training)
            stock_to_capacity_ratio = current_quantity / (manufacturing_capacity_per_day + 1)
            production_potential = manufacturing_capacity_per_day * lead_time_days
            stock_deficit = max(0, target_quantity - current_quantity)
            urgency_score = stock_deficit / (production_potential + 1)

            # Prepare the features for the model (must match training feature order)
            input_features = [
                current_quantity,
                manufacturing_capacity_per_day,
                lead_time_days,
                stock_to_capacity_ratio,
                production_potential,
                stock_deficit,
                urgency_score
            ]

            prediction = transfer_model.predict([input_features])
            return int(prediction[0])  # return 1 or 0
        except Exception as e:
            print("Prediction error:", e)
            return 0
    return 0

# User Management Views
@login_required
def user_management(request):
    """
    View for user management - shows different information based on user role
    """
    user = request.user
    role = get_role_display(user)

    # Get all users based on role hierarchy
    if is_admin(user):
        # Admins can see all users
        users = User.objects.all()
    elif is_manager(user):
        # Managers can see employees and themselves
        users = User.objects.filter(role__in=['staff', 'manager'])
    else:
        # Employees can only see themselves
        users = User.objects.filter(id=user.id)

    context = {
        'users': users,
        'user_role': role,
    }

    return render(request, 'inventory/user_management.html', context)

# Activity Log Views
@login_required
def activity_log(request):
    """
    View for activity logs - shows different information based on user role
    """
    from .models import ActivityLog

    user = request.user
    role = get_role_display(user)

    # Get activity logs based on role hierarchy
    if is_admin(user):
        # Admins can see all activity logs
        logs = ActivityLog.objects.all()
    elif is_manager(user):
        # Managers can see logs from employees and themselves
        employee_users = User.objects.filter(role='staff')
        logs = ActivityLog.objects.filter(
            user__in=list(employee_users) + [user]
        )
    else:
        # Employees can only see their own logs
        logs = ActivityLog.objects.filter(user=user)

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(logs, 20)  # Show 20 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'user_role': role,
    }

    return render(request, 'inventory/activity_log.html', context)


# Audit Log Views
@login_required
@user_passes_test(is_manager)  # Only managers and admins can access audit logs
def audit_log_list(request):
    """
    View for listing audit logs - managers and admins only
    """
    user = request.user
    role = get_role_display(user)

    # Get audit logs based on role hierarchy
    if is_admin(user):
        # Admins can see all audit logs
        audit_logs = AuditLog.objects.all()
    else:
        # Managers can see all audit logs too (as per requirements)
        audit_logs = AuditLog.objects.all()

    # Add search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        audit_logs = audit_logs.filter(
            Q(factory__name__icontains=search_query) |
            Q(notes__icontains=search_query) |
            Q(audited_by__username__icontains=search_query)
        )

    # Add pagination
    from django.core.paginator import Paginator
    paginator = Paginator(audit_logs, 10)  # Show 10 audit logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'user_role': role,
        'search_query': search_query,
        'total_audits': audit_logs.count(),
    }

    return render(request, 'inventory/audit_log_list.html', context)


@login_required
@user_passes_test(is_manager)  # Only managers and admins can access audit logs
def audit_log_detail(request, audit_id):
    """
    View for audit log details
    """
    audit_log = get_object_or_404(AuditLog, id=audit_id)
    user = request.user
    role = get_role_display(user)

    context = {
        'audit_log': audit_log,
        'user_role': role,
    }

    return render(request, 'inventory/audit_log_detail.html', context)


@login_required
@user_passes_test(is_manager)  # Only managers and admins can create audit logs
def audit_log_create(request):
    """
    View for creating new audit logs
    """
    user = request.user
    role = get_role_display(user)

    if request.method == 'POST':
        form = AuditLogForm(request.POST)
        if form.is_valid():
            audit_log = form.save(commit=False)
            audit_log.audited_by = request.user
            audit_log.save()

            messages.success(request, 'Audit log created successfully!')
            return redirect('audit_log_list')
    else:
        form = AuditLogForm()

    context = {
        'form': form,
        'user_role': role,
        'action': 'Create',
    }

    return render(request, 'inventory/audit_log_form.html', context)


@login_required
@user_passes_test(is_admin)  # Only admins can edit audit logs
def audit_log_edit(request, audit_id):
    """
    View for editing audit logs - admin only
    """
    audit_log = get_object_or_404(AuditLog, id=audit_id)
    user = request.user
    role = get_role_display(user)

    if request.method == 'POST':
        form = AuditLogForm(request.POST, instance=audit_log)
        if form.is_valid():
            form.save()
            messages.success(request, 'Audit log updated successfully!')
            return redirect('audit_log_detail', audit_id=audit_log.id)
    else:
        form = AuditLogForm(instance=audit_log)

    context = {
        'form': form,
        'audit_log': audit_log,
        'user_role': role,
        'action': 'Edit',
    }

    return render(request, 'inventory/audit_log_form.html', context)


@login_required
@user_passes_test(is_admin)  # Only admins can delete audit logs
def audit_log_delete(request, audit_id):
    """
    View for deleting audit logs - admin only
    """
    audit_log = get_object_or_404(AuditLog, id=audit_id)

    if request.method == 'POST':
        factory_name = audit_log.factory.name
        audit_log.delete()
        messages.success(request, f'Audit log for {factory_name} deleted successfully!')
        return redirect('audit_log_list')

    user = request.user
    role = get_role_display(user)

    context = {
        'audit_log': audit_log,
        'user_role': role,
    }

    return render(request, 'inventory/audit_log_confirm_delete.html', context)