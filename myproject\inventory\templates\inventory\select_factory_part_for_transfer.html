<!-- select_factory_part_for_transfer.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Select Factory and Part</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .home-button {
            background-color: white;
            color: #007bff;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            border: 2px solid white;
            transition: 0.3s;
        }
        .home-button:hover {
            background-color: #e6f0ff;
            color: #0056b3;
            border-color: #e6f0ff;
        }
        .container {
            max-width: 600px;
            margin: 60px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.1);
        }
        h2 {
            text-align: center;
            color: #007bff;
            margin-bottom: 30px;
        }
        label {
            display: block;
            margin: 10px 0;
            font-weight: 600;
        }
        select {
            width: 100%;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin-bottom: 20px;
        }
        .info-box {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #bee5eb;
            margin-bottom: 20px;
        }
        select option:first-child {
            color: #999;
        }
    </style>
</head>
<body>
    <header>
        <h1>logistiQ</h1>
        <a href="{% url 'dashboard' %}" class="home-button">Home</a>
    </header>

    <div class="container">
        <h2>Select Factory and Part for Transfer Prediction</h2>

        {% if error_message %}
            <div class="error-message">
                <strong>Error:</strong> {{ error_message }}
            </div>
        {% endif %}

        <div class="info-box">
            <p><strong>Note:</strong> Only factories and parts with existing transfer mappings are shown below.</p>
        </div>

        <form method="POST">
            {% csrf_token %}
            <div>
                <label for="factory">Select Destination Factory:</label>
                <select name="factory" id="factory" required>
                    <option value="">-- Choose a Factory --</option>
                    {% for factory in factories %}
                        <option value="{{ factory.id }}">{{ factory.name }} ({{ factory.location }})</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label for="part">Select Part:</label>
                <select name="part" id="part" required>
                    <option value="">-- Choose a Part --</option>
                    {% for part in parts %}
                        <option value="{{ part.id }}">{{ part.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <button type="submit" class="btn btn-primary">Check Transfer Prediction</button>
        </form>
    </div>
</body>
</html>
