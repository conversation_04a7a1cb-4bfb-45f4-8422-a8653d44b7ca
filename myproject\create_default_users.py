import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from accounts.models import CustomUser

def create_default_users():
    """
    Create the three default users with different roles if they don't exist
    """
    # Check if users already exist
    if CustomUser.objects.count() > 0:
        print("Users already exist. Skipping creation.")
        return
    
    # Create admin user
    admin_user = CustomUser.objects.create_user(
        username='admin',
        password='admin123',
        email='<EMAIL>',
        role='superuser',
        is_superuser=True,
        is_staff=True
    )
    print(f"Created admin user: {admin_user.username}")
    
    # Create manager user
    manager_user = CustomUser.objects.create_user(
        username='manager',
        password='manager123',
        email='<EMAIL>',
        role='manager'
    )
    print(f"Created manager user: {manager_user.username}")
    
    # Create employee user
    employee_user = CustomUser.objects.create_user(
        username='employee',
        password='employee123',
        email='<EMAIL>',
        role='staff'
    )
    print(f"Created employee user: {employee_user.username}")
    
    print("Default users created successfully!")

if __name__ == "__main__":
    create_default_users()
