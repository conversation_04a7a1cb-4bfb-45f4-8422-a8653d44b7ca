import re
from django.utils.deprecation import MiddlewareMixin
from django.urls import resolve
from django.contrib.contenttypes.models import ContentType
from .models import ActivityLog

class ActivityLogMiddleware(MiddlewareMixin):
    """
    Middleware to log user activity
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # Required for Django 5.2+
        self.async_mode = False
        # Patterns to ignore (static files, admin, etc.)
        self.ignored_patterns = [
            r'^/static/',
            r'^/media/',
            r'^/admin/jsi18n/',
            r'^/favicon.ico',
        ]

    def should_log(self, request):
        """
        Determine if the request should be logged
        """
        # Don't log ignored patterns
        path = request.path
        for pattern in self.ignored_patterns:
            if re.match(pattern, path):
                return False

        # Only log authenticated users
        if not request.user.is_authenticated:
            return False

        # Don't log AJAX requests
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return False

        return True

    def process_view(self, request, view_func, view_args, view_kwargs):
        """
        Process the view and log the activity if needed
        """
        try:
            if not self.should_log(request):
                return None

            # Get the view name
            view_name = view_func.__name__ if hasattr(view_func, '__name__') else str(view_func)

            # Determine the action based on the request method
            action_map = {
                'GET': 'VIEW',
                'POST': 'CREATE' if 'create' in view_name.lower() else 'UPDATE',
                'DELETE': 'DELETE',
                'PUT': 'UPDATE',
                'PATCH': 'UPDATE',
            }

            action = action_map.get(request.method, 'OTHER')

            # Try to determine the model being affected
            model_name = ''
            object_repr = ''
            content_type = None
            object_id = None

            # Try to get the model from the view kwargs
            for key, value in view_kwargs.items():
                if key.endswith('_id') and value:
                    model_name = key.replace('_id', '')
                    object_id = value
                    try:
                        # Try to get the model class
                        url_name = resolve(request.path_info).url_name
                        if 'factory' in url_name:
                            from .models import Factory
                            obj = Factory.objects.get(id=value)
                            content_type = ContentType.objects.get_for_model(Factory)
                            object_repr = str(obj)
                        elif 'part' in url_name:
                            from .models import Part
                            obj = Part.objects.get(id=value)
                            content_type = ContentType.objects.get_for_model(Part)
                            object_repr = str(obj)
                        elif 'inventory' in url_name:
                            from .models import Inventory
                            obj = Inventory.objects.get(id=value)
                            content_type = ContentType.objects.get_for_model(Inventory)
                            object_repr = str(obj)
                    except Exception:
                        pass

            # Create the activity log - only if we can do so safely
            if request.user.is_authenticated:
                ActivityLog.objects.create(
                    user=request.user,
                    action=action,
                    model_name=model_name,
                    object_repr=object_repr,
                    content_type=content_type,
                    object_id=object_id,
                    details=f"URL: {request.path}, Method: {request.method}, View: {view_name}",
                    ip_address=self.get_client_ip(request)
                )

            return None
        except Exception as e:
            # Log the error but don't interrupt the request
            print(f"Error in ActivityLogMiddleware: {str(e)}")
            return None

    def get_client_ip(self, request):
        """
        Get the client IP address
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def __call__(self, request):
        """
        Override the __call__ method to ensure compatibility with Django 5.2+
        """
        response = self.get_response(request)
        return response
