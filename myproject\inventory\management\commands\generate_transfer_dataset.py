from django.core.management.base import BaseCommand
from inventory.models import Factory, Part, FactoryPart, Inventory
import pandas as pd
from datetime import datetime
import os
import random  # Added for randomness

class Command(BaseCommand):
    help = 'Generate training dataset for ML-based Transfer Recommendations'

    def handle(self, *args, **kwargs):
        data = []

        all_factories = Factory.objects.all()
        all_parts = Part.objects.all()

        for factory in all_factories:
            for part in all_parts:
                # Inventory level at this factory
                try:
                    inventory = Inventory.objects.get(factory=factory, part=part)
                    stock = inventory.current_quantity 
                except Inventory.DoesNotExist:
                    stock = 0

                # FactoryPart info for this factory-part
                try:
                    fp = FactoryPart.objects.get(factory=factory, part=part)
                    target_quantity = fp.target_quantity
                except FactoryPart.DoesNotExist:
                    target_quantity = 0

                stock_deficit = max(0, target_quantity - stock)

                # Get supplier factories that manufacture this part
                supplier_links = FactoryPart.objects.filter(part=part).exclude(factory=factory)
                supplier_count = supplier_links.count()
                fastest_lead_time = min([s.lead_time_days for s in supplier_links], default=0)
                max_supplier_capacity = max([s.manufacturing_capacity_per_day for s in supplier_links], default=0)

                # 💡 Updated label logic to include randomness
                if stock < target_quantity and supplier_count > 0:
                    label = 1
                elif random.random() < 0.3:  # ~30% chance to add 1s for balance
                    label = 1
                else:
                    label = 0

                data.append({
                    'date': datetime.now().date(),
                    'factory_id': factory.id,
                    'part_id': part.id,
                    'current_stock': stock,
                    'target_quantity': target_quantity,
                    'stock_deficit': stock_deficit,
                    'supplier_count': supplier_count,
                    'fastest_lead_time': fastest_lead_time,
                    'max_supplier_capacity': max_supplier_capacity,
                    'transfer_required': label
                })

        # Convert to DataFrame and save
        df = pd.DataFrame(data)
        output_path = os.path.join('ml_datasets', 'transfer_dataset.csv')
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        df.to_csv(output_path, index=False)

        self.stdout.write(self.style.SUCCESS(f"Transfer dataset generated at {output_path}"))
