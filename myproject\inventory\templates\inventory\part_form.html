{% extends 'inventory/base.html' %}

{% block content %}
<div class="content-box">
  <h1>{{ app_name }} - {% if form.instance.pk %}Edit Part{% else %}Add New Part{% endif %}</h1>

  <a href="{% url 'part_list' %}" class="btn btn-primary mb-3">Back to Part List</a>

  <form method="POST" class="needs-validation" novalidate>
    {% csrf_token %}
    
    <div class="row">
      {% for field in form %}
        <div class="mb-3 col-md-6">
          <label class="form-label fw-semibold" for="{{ field.id_for_label }}">{{ field.label }}</label>
          <div class="input-group">
            {{ field }}
          </div>
          {% if field.help_text %}
            <div class="form-text">{{ field.help_text }}</div>
          {% endif %}
          {% for error in field.errors %}
            <div class="text-danger small">{{ error }}</div>
          {% endfor %}
        </div>
      {% endfor %}
    </div>

    <button type="submit" class="btn btn-success mt-2">
      {% if form.instance.pk %}Update Part{% else %}Add Part{% endif %}
    </button>
  </form>
</div>
{% endblock %}
