{% extends 'inventory/base.html' %}

{% block content %}
<div class="content-box">
  {% if app_name %}
    <h1>{{ app_name }} - Parts List</h1>
  {% else %}
    <h1>Parts List</h1>
  {% endif %}

  <a href="{% url 'dashboard' %}" class="btn btn-primary">Home</a>
  <a href="{% url 'part_create' %}" class="btn btn-success ml-2">Add New Part</a>

  <table class="table table-striped table-bordered mt-4">
      <thead class="thead-dark">
          <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Description</th>
              <th>Actions</th>
          </tr>
      </thead>
      <tbody>
          {% for part in parts %}
          <tr>
              <td>{{ part.id }}</td>
              <td>{{ part.name }}</td>
              <td>{{ part.description }}</td>
              <td>
                  <a href="{% url 'part_update' part.id %}" class="btn btn-warning btn-sm">Edit</a>
                  <a href="{% url 'part_delete' part.id %}" class="btn btn-danger btn-sm">Delete</a>
              </td>
          </tr>
          {% endfor %}
      </tbody>
  </table>
</div>
{% endblock %}
