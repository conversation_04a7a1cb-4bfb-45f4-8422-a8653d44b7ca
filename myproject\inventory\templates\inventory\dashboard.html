<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Dashboard - logistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 800px;
            margin: 60px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.1);
        }
        h2 {
            text-align: center;
            color: #007bff;
            margin-bottom: 30px;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        li {
            margin-bottom: 15px;
        }
        li a {
            display: block;
            padding: 12px 20px;
            background-color: #e9f3ff;
            color: #007bff;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }
        li a:hover {
            background-color: #d0e7ff;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>logistiQ</h1>
        <div class="nav-links">
            <a href="{% url 'dashboard' %}">Home</a>
            <a href="{% url 'logout' %}">Logout</a>
        </div>
    </header>

    <div class="container">
        <h2>Dashboard</h2>
        <ul>
            <li><a href="{% url 'factory_list' %}">Factory List</a></li>
            <li><a href="{% url 'part_list' %}">Part List</a></li>
            <li><a href="{% url 'factory_part_list' %}">Factory-Part Mapping</a></li>
            <li><a href="{% url 'inventory_list' %}">Inventory</a></li>
            <li><a href="{% url 'production_log_dashboard' %}">Product Logs</a></li>

            <!-- Styled link instead of button -->
            <li><a href="{% url 'select_factory_part_for_transfer' %}">Check Transfer Prediction</a></li>

            <!-- Check Transfer Prediction Links for all factories and parts -->
            {% for factory in factories %}
                {% for part in parts %}
                    <li>
                        <a href="{% url 'check_transfer' factory_id=factory.id part_id=part.id %}">
                            Check Transfer Prediction for {{ factory.name }} and {{ part.name }}
                        </a>
                    </li>
                {% endfor %}
            {% endfor %}

            <li><a href="#">Audit Logs</a></li>
            <li><a href="#">User Management</a></li>
        </ul>
    </div>

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
