<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Audit Log Details - LogistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #003366;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-role {
            background-color: #ff9800;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 1000px;
            margin: 40px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 51, 102, 0.1);
        }
        h2 {
            color: #003366;
            margin-bottom: 30px;
        }
        .audit-header {
            background-color: #003366;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .audit-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        .info-value {
            color: #6c757d;
        }
        .audit-notes {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .btn-primary {
            background-color: #003366;
            border-color: #003366;
        }
        .btn-primary:hover {
            background-color: #002244;
            border-color: #002244;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>LogistiQ</h1>
        <div class="user-info">
            <span class="user-role">{{ user_role|upper }}</span>
            <div class="nav-links">
                <a href="{% url 'dashboard' %}">Dashboard</a>
                <a href="{% url 'audit_log_list' %}">Audit Logs</a>
                <a href="{% url 'user_management' %}">User Management</a>
                <a href="{% url 'logout' %}">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clipboard-list me-2"></i>Audit Log Details</h2>
            <a href="{% url 'audit_log_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>

        <!-- Audit Header -->
        <div class="audit-header">
            <div class="row">
                <div class="col-md-8">
                    <h3 class="mb-2">
                        <i class="fas fa-industry me-2"></i>{{ audit_log.factory.name }}
                    </h3>
                    <p class="mb-0">Factory Audit Report</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="action-buttons">
                        {% if user_role == 'Admin' %}
                            <a href="{% url 'audit_log_edit' audit_log.id %}" class="btn btn-light btn-sm">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                            <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Information -->
        <div class="audit-info">
            <h4 class="mb-3"><i class="fas fa-info-circle me-2"></i>Audit Information</h4>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-industry me-1"></i>Factory:
                </span>
                <span class="info-value">{{ audit_log.factory.name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-user me-1"></i>Audited By:
                </span>
                <span class="info-value">
                    {{ audit_log.audited_by.username }}
                    {% if audit_log.audited_by.first_name or audit_log.audited_by.last_name %}
                        ({{ audit_log.audited_by.first_name }} {{ audit_log.audited_by.last_name }})
                    {% endif %}
                </span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-calendar me-1"></i>Audit Date:
                </span>
                <span class="info-value">{{ audit_log.audit_date|date:"F j, Y, g:i a" }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">
                    <i class="fas fa-clock me-1"></i>Days Ago:
                </span>
                <span class="info-value">{{ audit_log.audit_date|timesince }} ago</span>
            </div>
        </div>

        <!-- Audit Notes -->
        <div class="audit-notes">
            <h4 class="mb-3"><i class="fas fa-sticky-note me-2"></i>Audit Notes & Findings</h4>
            <div class="notes-content">
                {{ audit_log.notes|linebreaks }}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{% url 'audit_log_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i>View All Audits
            </a>
            <a href="{% url 'audit_log_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>New Audit
            </a>
            {% if user_role == 'Admin' %}
                <a href="{% url 'audit_log_edit' audit_log.id %}" class="btn btn-warning">
                    <i class="fas fa-edit me-1"></i>Edit Audit
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    {% if user_role == 'Admin' %}
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this audit log for <strong>{{ audit_log.factory.name }}</strong>?</p>
                    <p class="text-danger"><small>This action cannot be undone.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="{% url 'audit_log_delete' audit_log.id %}" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">Delete Audit Log</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
