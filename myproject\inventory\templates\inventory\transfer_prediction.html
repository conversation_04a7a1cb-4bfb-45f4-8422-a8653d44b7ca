<!-- transfer_prediction.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Prediction</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f0f8ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .back-button {
            background-color: white;
            color: #007bff;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            border: 2px solid white;
            transition: 0.3s;
        }
        .back-button:hover {
            background-color: #e6f0ff;
            color: #0056b3;
            border-color: #e6f0ff;
        }
        .container {
            max-width: 700px;
            margin: 60px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.1);
        }
        h2 {
            text-align: center;
            color: #007bff;
            margin-bottom: 30px;
        }
        p {
            font-size: 18px;
            margin: 10px 0;
        }
        strong {
            color: #0056b3;
        }
        h3 {
            text-align: center;
            margin-top: 30px;
            color: #333;
        }
        .prediction-result {
            background-color: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .prediction-yes {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .prediction-no {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .location-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .source-factories {
            margin-top: 20px;
        }
        .source-factory {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .detail-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <header>
        <h1>Transfer Prediction</h1>
        <a href="javascript:history.back()" class="back-button">Back</a>
    </header>

    <div class="container">
        <h2>Transfer Prediction Results</h2>

        <!-- Location Information -->
        <div class="location-info">
            <h3>📍 Destination Location</h3>
            <p><strong>Factory:</strong> {{ factory.name }}</p>
            <p><strong>Location:</strong> {{ factory.location }}</p>
            <p><strong>Part Required:</strong> {{ part.name }}</p>
        </div>

        <!-- Prediction Result -->
        {% if prediction %}
            <div class="prediction-result {% if prediction == 'Yes' %}prediction-yes{% else %}prediction-no{% endif %}">
                <h3>🔮 Transfer Prediction: <strong>{{ prediction }}</strong></h3>
                {% if prediction == 'Yes' %}
                    <p>⚠️ A transfer is recommended for this factory-part combination.</p>
                {% else %}
                    <p>✅ No transfer is currently needed for this factory-part combination.</p>
                {% endif %}
            </div>
        {% endif %}

        <!-- Detailed Information -->
        <div class="details-grid">
            <div class="detail-item">
                <strong>Current Stock:</strong><br>
                {{ inventory.current_quantity|default:"0" }} units
            </div>
            {% if factory_part %}
            <div class="detail-item">
                <strong>Target Quantity:</strong><br>
                {{ factory_part.target_quantity }} units
            </div>
            <div class="detail-item">
                <strong>Manufacturing Capacity:</strong><br>
                {{ factory_part.manufacturing_capacity_per_day }} units/day
            </div>
            <div class="detail-item">
                <strong>Lead Time:</strong><br>
                {{ factory_part.lead_time_days }} days
            </div>
            {% endif %}
        </div>

        <!-- Potential Source Factories -->
        {% if potential_sources %}
            <div class="source-factories">
                <h3>🏭 Potential Source Factories</h3>
                <p>The following factories can manufacture <strong>{{ part.name }}</strong> and could serve as transfer sources:</p>
                {% for source in potential_sources %}
                    <div class="source-factory">
                        <strong>{{ source.factory.name }}</strong> ({{ source.factory.location }})
                        <br>
                        <small>
                            Capacity: {{ source.manufacturing_capacity_per_day }} units/day |
                            Lead Time: {{ source.lead_time_days }} days
                        </small>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="source-factories">
                <h3>⚠️ No Alternative Sources</h3>
                <p>No other factories are currently configured to manufacture <strong>{{ part.name }}</strong>.</p>
            </div>
        {% endif %}
    </div>
</body>
</html>
