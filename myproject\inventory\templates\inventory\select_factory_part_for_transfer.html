<!-- select_factory_part_for_transfer.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Select Factory and Part</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #007bff;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .home-button {
            background-color: white;
            color: #007bff;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            border: 2px solid white;
            transition: 0.3s;
        }
        .home-button:hover {
            background-color: #e6f0ff;
            color: #0056b3;
            border-color: #e6f0ff;
        }
        .container {
            max-width: 600px;
            margin: 60px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.1);
        }
        h2 {
            text-align: center;
            color: #007bff;
            margin-bottom: 30px;
        }
        label {
            display: block;
            margin: 10px 0;
            font-weight: 600;
        }
        select {
            width: 100%;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <header>
        <h1>logistiQ</h1>
        <a href="{% url 'dashboard' %}" class="home-button">Home</a>
    </header>

    <div class="container">
        <h2>Select Factory and Part</h2>
        <form method="POST">
            {% csrf_token %}
            <div>
                <label for="factory">Select Factory:</label>
                <select name="factory" id="factory">
                    {% for factory in factories %}
                        <option value="{{ factory.id }}">{{ factory.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label for="part">Select Part:</label>
                <select name="part" id="part">
                    {% for part in parts %}
                        <option value="{{ part.id }}">{{ part.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <button type="submit" class="btn btn-primary">Check Transfer Prediction</button>
        </form>
    </div>
</body>
</html>
