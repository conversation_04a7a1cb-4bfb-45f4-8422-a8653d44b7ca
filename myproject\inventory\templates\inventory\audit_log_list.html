<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Audit Logs - LogistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #003366;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-role {
            background-color: #ff9800;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 1200px;
            margin: 40px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 51, 102, 0.1);
        }
        h2 {
            text-align: center;
            color: #003366;
            margin-bottom: 30px;
        }
        .search-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .audit-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .audit-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 51, 102, 0.1);
        }
        .audit-header {
            background-color: #003366;
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .audit-body {
            padding: 20px;
        }
        .audit-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #6c757d;
        }
        .btn-primary {
            background-color: #003366;
            border-color: #003366;
        }
        .btn-primary:hover {
            background-color: #002244;
            border-color: #002244;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .stats-row {
            background-color: #e9f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .pagination {
            justify-content: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>LogistiQ</h1>
        <div class="user-info">
            <span class="user-role">{{ user_role|upper }}</span>
            <div class="nav-links">
                <a href="{% url 'dashboard' %}">Dashboard</a>
                <a href="{% url 'user_management' %}">User Management</a>
                <a href="{% url 'activity_log' %}">Activity Logs</a>
                <a href="{% url 'logout' %}">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <h2><i class="fas fa-clipboard-list me-2"></i>Audit Logs</h2>
        
        <!-- Statistics -->
        <div class="stats-row">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="mb-1">{{ total_audits }}</h5>
                    <small class="text-muted">Total Audits</small>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-1">{{ page_obj.paginator.count }}</h5>
                    <small class="text-muted">Filtered Results</small>
                </div>
                <div class="col-md-4">
                    <a href="{% url 'audit_log_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>New Audit
                    </a>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <form method="GET" class="row g-3">
                <div class="col-md-10">
                    <input type="text" class="form-control" name="search" 
                           placeholder="Search by factory name, auditor, or notes..." 
                           value="{{ search_query }}">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </form>
            {% if search_query %}
                <div class="mt-2">
                    <small class="text-muted">
                        Showing results for: <strong>"{{ search_query }}"</strong>
                        <a href="{% url 'audit_log_list' %}" class="ms-2">Clear search</a>
                    </small>
                </div>
            {% endif %}
        </div>

        <!-- Audit Logs -->
        {% for audit in page_obj %}
            <div class="audit-card">
                <div class="audit-header">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-industry me-2"></i>{{ audit.factory.name }}
                        </h5>
                    </div>
                    <div>
                        <a href="{% url 'audit_log_detail' audit.id %}" class="btn btn-light btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                    </div>
                </div>
                <div class="audit-body">
                    <div class="audit-meta">
                        <span>
                            <i class="fas fa-user me-1"></i>
                            Audited by: <strong>{{ audit.audited_by.username }}</strong>
                        </span>
                        <span>
                            <i class="fas fa-calendar me-1"></i>
                            {{ audit.audit_date|date:"F j, Y, g:i a" }}
                        </span>
                    </div>
                    <div class="audit-notes">
                        <p class="mb-0">
                            {{ audit.notes|truncatewords:30 }}
                            {% if audit.notes|length > 150 %}
                                <a href="{% url 'audit_log_detail' audit.id %}" class="text-primary">Read more...</a>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No audit logs found</h4>
                <p class="text-muted">
                    {% if search_query %}
                        No audit logs match your search criteria.
                    {% else %}
                        No audit logs have been created yet.
                    {% endif %}
                </p>
                <a href="{% url 'audit_log_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create First Audit Log
                </a>
            </div>
        {% endfor %}

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <nav aria-label="Audit log pagination">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    </div>

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
