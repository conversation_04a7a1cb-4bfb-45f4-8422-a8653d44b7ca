import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import joblib

# Load the dataset (make sure it's the one with the correct columns)
df = pd.read_csv('inventory/ml_models/transfer_dataset.csv')

# Debugging: Check if dataset is loaded properly
print("Dataset Loaded:")
print(df.head())

# Use only the actual feature columns from your logic
X = df[['current_quantity', 'manufacturing_capacity_per_day', 'lead_time_days']]
y = df['transfer_required']

# Debugging: Check X and y before train_test_split
print(f"X shape: {X.shape}, y shape: {y.shape}")
print(f"X preview: {X.head()}")
print(f"y preview: {y.head()}")

# Check if there is data to train on
if X.empty or y.empty:
    raise ValueError("The features (X) or labels (y) are empty. Please check the dataset.")

# Do not balance the dataset (keep original distribution)
# X = df[['current_quantity', 'manufacturing_capacity_per_day', 'lead_time_days']]
# y = df['transfer_required']

# Split the dataset into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)

# Check if there are enough samples
if X_train.shape[0] == 0 or X_test.shape[0] == 0:
    raise ValueError("Not enough samples in the dataset after splitting.")

# Train the model
model = RandomForestClassifier()
model.fit(X_train, y_train)

# Evaluate
print(classification_report(y_test, model.predict(X_test)))

# Save model
joblib.dump(model, 'inventory/ml_models/transfer_model.pkl')
