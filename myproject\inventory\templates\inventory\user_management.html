<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>User Management - LogistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #003366;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-role {
            background-color: #ff9800;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 1000px;
            margin: 40px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 51, 102, 0.1);
        }
        h2 {
            text-align: center;
            color: #003366;
            margin-bottom: 30px;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .role-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        .role-admin {
            background-color: #dc3545;
            color: white;
        }
        .role-manager {
            background-color: #28a745;
            color: white;
        }
        .role-employee {
            background-color: #17a2b8;
            color: white;
        }
        .user-card {
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .user-card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            border-radius: 10px 10px 0 0;
            padding: 15px;
        }
        .card-body {
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>LogistiQ</h1>
        <div class="user-info">
            <span class="user-role">{{ user_role|upper }}</span>
            <div class="nav-links">
                <a href="{% url 'dashboard' %}">Dashboard</a>
                <a href="{% url 'activity_log' %}">Activity Log</a>
                <a href="{% url 'logout' %}">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <h2>User Management</h2>
        
        <div class="row">
            {% for user in users %}
                <div class="col-md-6">
                    <div class="card user-card">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ user.username }}</h5>
                            {% if user.role == 'superuser' %}
                                <span class="role-badge role-admin">Admin</span>
                            {% elif user.role == 'manager' %}
                                <span class="role-badge role-manager">Manager</span>
                            {% else %}
                                <span class="role-badge role-employee">Employee</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <p><strong>Email:</strong> {{ user.email|default:"Not provided" }}</p>
                            <p><strong>Last Login:</strong> {{ user.last_login|date:"F j, Y, g:i a"|default:"Never" }}</p>
                            <p><strong>Date Joined:</strong> {{ user.date_joined|date:"F j, Y" }}</p>
                            
                            <div class="mt-3">
                                <a href="{% url 'activity_log' %}?user={{ user.id }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-history"></i> View Activity
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="col-12 text-center">
                    <p class="text-muted">No users found.</p>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
