{% extends 'inventory/base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>Inventory List</h2>
    <a href="{% url 'inventory_create' %}" class="btn btn-primary mb-3">Add Inventory</a>
    <table class="table table-bordered table-hover">
        <thead class="thead-light">
            <tr>
                <th>Factory</th>
                <th>Part</th>
                <th>Quantity in Stock</th>
                <th>Last Updated</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for item in inventory %}
            <tr>
                <td>{{ item.factory.name }}</td>
                <td>{{ item.part.name }}</td>
                <td>{{ item.current_quantity }}</td>  <!-- Corrected field name -->
                <td>{{ item.updated_at|date:"Y-m-d H:i" }}</td>
                <td>
                    <a href="{% url 'inventory_update' item.pk %}" class="btn btn-sm btn-info">Edit</a>
                    <a href="{% url 'inventory_delete' item.pk %}" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5">No inventory items found </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
