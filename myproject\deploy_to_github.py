#!/usr/bin/env python3
"""
Script to help deploy the project to GitHub
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SUCCESS")
            if result.stdout:
                print("Output:")
                print(result.stdout)
        else:
            print("❌ ERROR")
            if result.stderr:
                print("Error output:")
                print(result.stderr)
            if result.stdout:
                print("Standard output:")
                print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False
    
    return True

def main():
    """Main deployment function"""
    print("🚀 PREPARING PROJECT FOR GITHUB DEPLOYMENT")
    print("=" * 60)
    
    # Check if git is initialized
    if not os.path.exists('.git'):
        print("Initializing Git repository...")
        if not run_command("git init", "Initialize Git Repository"):
            return
    
    # Add all files
    if not run_command("git add .", "Add All Files to Git"):
        return
    
    # Commit changes
    commit_message = "Prepare Django project for deployment with Gunicorn and WhiteNoise"
    if not run_command(f'git commit -m "{commit_message}"', "Commit Changes"):
        print("Note: If no changes to commit, this is normal.")
    
    print(f"\n{'='*60}")
    print("🎉 PROJECT READY FOR GITHUB!")
    print(f"{'='*60}")
    print("\nNext steps:")
    print("1. Create a repository on GitHub")
    print("2. Add the remote origin:")
    print("   git remote add origin https://github.com/yourusername/yourrepo.git")
    print("3. Push to GitHub:")
    print("   git push -u origin main")
    print("\nThen deploy on Render.com using your GitHub repository!")

if __name__ == "__main__":
    main()
