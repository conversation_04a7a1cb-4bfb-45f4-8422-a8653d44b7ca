from django.db import models
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils import timezone
# 🏭 Factory model
class Factory(models.Model):
    name = models.CharField(max_length=100)
    location = models.CharField(max_length=200)

    def __str__(self):
        return self.name

# ⚙️ Part model
class Part(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

# 🔄 Factory-Part Mapping
class FactoryPart(models.Model):
    factory = models.ForeignKey(Factory, on_delete=models.CASCADE)
    part = models.ForeignKey(Part, on_delete=models.CASCADE)
    target_quantity = models.PositiveIntegerField()
    manufacturing_capacity_per_day = models.PositiveIntegerField(
        help_text="Maximum units the factory can produce per day",
        default=0
    )
    lead_time_days = models.PositiveIntegerField(
        help_text="Expected lead time in days to produce the part",
        default=0
    )
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('factory', 'part')
        verbose_name = "Factory-Part Mapping"
        verbose_name_plural = "Factory-Part Mappings"

    def __str__(self):
        return f"{self.factory} manufactures {self.part}"

# 📦 Inventory model for stock levels
class Inventory(models.Model):
    factory = models.ForeignKey(Factory, on_delete=models.CASCADE)
    part = models.ForeignKey(Part, on_delete=models.CASCADE)
    current_quantity = models.PositiveIntegerField()
    updated_at = models.DateTimeField(auto_now=True)  # ✅ Ensures latest timestamp is stored on update

    class Meta:
        unique_together = ('factory', 'part')

    def __str__(self):
        return f"{self.part.name} in {self.factory.name}: {self.current_quantity}"

# 🧾 Production log (used for inventory updates & ML training)
class ProductionLog(models.Model):
    factory = models.ForeignKey(Factory, on_delete=models.CASCADE)
    part = models.ForeignKey(Part, on_delete=models.CASCADE)
    quantity_produced = models.PositiveIntegerField()
    timestamp = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return f"{self.quantity_produced} of {self.part.name} at {self.factory.name}"

# 🚚 Transfer log (used for tracking inter-factory movement)
class TransferLog(models.Model):
    from_factory = models.ForeignKey(Factory, on_delete=models.CASCADE, related_name='transfers_out')
    to_factory = models.ForeignKey(Factory, on_delete=models.CASCADE, related_name='transfers_in')
    part = models.ForeignKey(Part, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    timestamp = models.DateTimeField(auto_now_add=True)
    transferred_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return f"Transferred {self.quantity} of {self.part.name} from {self.from_factory} to {self.to_factory}"

# 📝 Audit log (can be used to track anomalies or for compliance)
class AuditLog(models.Model):
    factory = models.ForeignKey(Factory, on_delete=models.CASCADE)
    audited_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    notes = models.TextField()
    audit_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Audit at {self.factory.name} on {self.audit_date.strftime('%Y-%m-%d')}"

# User Activity Log for tracking all changes
class ActivityLog(models.Model):
    ACTION_CHOICES = [
        ('CREATE', 'Created'),
        ('UPDATE', 'Updated'),
        ('DELETE', 'Deleted'),
        ('LOGIN', 'Logged In'),
        ('LOGOUT', 'Logged Out'),
        ('VIEW', 'Viewed'),
        ('OTHER', 'Other Action')
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)
    timestamp = models.DateTimeField(default=timezone.now)

    # Information about the affected model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    # Additional information
    model_name = models.CharField(max_length=100, blank=True)  # Name of the model affected
    object_repr = models.CharField(max_length=200, blank=True)  # String representation of the object
    details = models.TextField(blank=True)  # Additional details about the action
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Activity Log'
        verbose_name_plural = 'Activity Logs'

    def __str__(self):
        if self.user:
            return f"{self.user.username} {self.get_action_display()} {self.model_name} at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"
        return f"Anonymous {self.get_action_display()} {self.model_name} at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"
