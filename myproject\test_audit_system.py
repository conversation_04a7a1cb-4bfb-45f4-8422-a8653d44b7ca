#!/usr/bin/env python
"""
Test script to verify the audit log system is working correctly
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
django.setup()

from inventory.models import AuditLog, Factory
from accounts.models import CustomUser

def test_audit_system():
    """Test the audit log system"""
    print("Testing Audit Log System...")
    print("=" * 50)
    
    # Check if we have any users
    users = CustomUser.objects.all()
    print(f"Total users in system: {users.count()}")
    
    if users.count() == 0:
        print("No users found. Creating test users...")
        # Create test users
        admin_user = CustomUser.objects.create_user(
            username='admin',
            password='admin123',
            role='superuser',
            is_superuser=True,
            is_staff=True
        )
        manager_user = CustomUser.objects.create_user(
            username='manager',
            password='manager123',
            role='manager'
        )
        employee_user = CustomUser.objects.create_user(
            username='employee',
            password='employee123',
            role='staff'
        )
        print("Test users created successfully!")
    
    # Check if we have any factories
    factories = Factory.objects.all()
    print(f"Total factories in system: {factories.count()}")
    
    if factories.count() == 0:
        print("No factories found. Creating test factory...")
        test_factory = Factory.objects.create(
            name="Test Factory",
            location="Test Location"
        )
        print("Test factory created successfully!")
    else:
        test_factory = factories.first()
    
    # Check existing audit logs
    audit_logs = AuditLog.objects.all()
    print(f"Existing audit logs: {audit_logs.count()}")
    
    # Create a test audit log
    admin_user = CustomUser.objects.filter(role='superuser').first()
    if admin_user:
        test_audit = AuditLog.objects.create(
            factory=test_factory,
            audited_by=admin_user,
            notes="This is a test audit log created by the test script. "
                  "The factory has been inspected and found to be in good condition. "
                  "All safety protocols are being followed. "
                  "Recommendation: Continue current operations."
        )
        print(f"Test audit log created with ID: {test_audit.id}")
        print(f"Audit details: {test_audit}")
    
    # Display all audit logs
    print("\nAll Audit Logs:")
    print("-" * 30)
    for audit in AuditLog.objects.all():
        print(f"ID: {audit.id}")
        print(f"Factory: {audit.factory.name}")
        print(f"Audited by: {audit.audited_by.username}")
        print(f"Date: {audit.audit_date}")
        print(f"Notes: {audit.notes[:100]}...")
        print("-" * 30)
    
    print("\nAudit Log System Test Completed Successfully!")
    print("You can now access the audit logs through the web interface.")
    print("URLs to test:")
    print("- Audit Log List: http://127.0.0.1:8000/audit-logs/")
    print("- Create New Audit: http://127.0.0.1:8000/audit-logs/create/")
    print("- Admin Dashboard: http://127.0.0.1:8000/dashboard/")

if __name__ == "__main__":
    test_audit_system()
