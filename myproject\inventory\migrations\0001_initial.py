# Generated by Django 5.2 on 2025-04-26 05:51

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Factory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('location', models.CharField(max_length=200)),
            ],
        ),
        migrations.CreateModel(
            name='Part',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('role', models.CharField(choices=[('manager', 'Manager'), ('staff', 'Staff'), ('employee', 'Employee')], max_length=20)),
                ('groups', models.ManyToManyField(blank=True, related_name='inventory_user_set', to='auth.group')),
                ('user_permissions', models.ManyToManyField(blank=True, related_name='inventory_user_permissions_set', to='auth.permission')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='TransferLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('from_factory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_out', to='inventory.factory')),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.part')),
                ('to_factory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_in', to='inventory.factory')),
                ('transferred_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.user')),
            ],
        ),
        migrations.CreateModel(
            name='ProductionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_produced', models.PositiveIntegerField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('factory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.factory')),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.part')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.user')),
            ],
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notes', models.TextField()),
                ('audit_date', models.DateTimeField(auto_now_add=True)),
                ('factory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.factory')),
                ('audited_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.user')),
            ],
        ),
        migrations.CreateModel(
            name='Inventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_quantity', models.PositiveIntegerField()),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('factory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.factory')),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.part')),
            ],
            options={
                'unique_together': {('factory', 'part')},
            },
        ),
        migrations.CreateModel(
            name='FactoryPart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('target_quantity', models.PositiveIntegerField()),
                ('manufacturing_capacity_per_day', models.PositiveIntegerField(default=0, help_text='Maximum units the factory can produce per day')),
                ('lead_time_days', models.PositiveIntegerField(default=0, help_text='Expected lead time in days to produce the part')),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('factory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.factory')),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.part')),
            ],
            options={
                'verbose_name': 'Factory-Part Mapping',
                'verbose_name_plural': 'Factory-Part Mappings',
                'unique_together': {('factory', 'part')},
            },
        ),
    ]
