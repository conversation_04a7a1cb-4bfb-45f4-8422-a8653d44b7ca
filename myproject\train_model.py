import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler
import joblib
import os

def load_and_validate_dataset(dataset_path):
    """Load and validate the dataset"""
    if not os.path.exists(dataset_path):
        raise FileNotFoundError(f"Dataset not found at {dataset_path}")

    df = pd.read_csv(dataset_path)
    print(f"Dataset loaded successfully with {len(df)} records")

    # Check for required columns
    required_columns = ['current_quantity', 'manufacturing_capacity_per_day', 'lead_time_days', 'transfer_required']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")

    return df

def preprocess_data(df):
    """Preprocess the data and create additional features"""
    # Create additional features for better prediction
    df['stock_to_capacity_ratio'] = df['current_quantity'] / (df['manufacturing_capacity_per_day'] + 1)
    df['production_potential'] = df['manufacturing_capacity_per_day'] * df['lead_time_days']
    df['stock_deficit'] = np.maximum(0, df['target_quantity'] - df['current_quantity'])
    df['urgency_score'] = df['stock_deficit'] / (df['production_potential'] + 1)

    # Handle any infinite or NaN values
    df = df.replace([np.inf, -np.inf], np.nan)
    df = df.fillna(0)

    return df

def train_and_evaluate_models(X_train, X_test, y_train, y_test):
    """Train multiple models and select the best one"""
    models = {
        'RandomForest': RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        ),
        'GradientBoosting': GradientBoostingClassifier(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
    }

    best_model = None
    best_score = 0
    best_name = ""

    print("\nTraining and evaluating models:")
    print("=" * 50)

    for name, model in models.items():
        # Train the model
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)

        # Calculate accuracy
        accuracy = accuracy_score(y_test, y_pred)

        # Cross-validation score
        cv_scores = cross_val_score(model, X_train, y_train, cv=5)

        print(f"\n{name} Results:")
        print(f"Accuracy: {accuracy:.4f}")
        print(f"Cross-validation score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        print(f"Classification Report:")
        print(classification_report(y_test, y_pred))

        if accuracy > best_score:
            best_score = accuracy
            best_model = model
            best_name = name

    print(f"\nBest model: {best_name} with accuracy: {best_score:.4f}")
    return best_model, best_name

# Main execution
print("Enhanced ML Model Training for Transfer Prediction")
print("=" * 60)

# Load the dataset
dataset_path = 'inventory/ml_models/transfer_dataset.csv'
df = load_and_validate_dataset(dataset_path)

# Display dataset statistics
print(f"\nDataset Statistics:")
print(f"Total records: {len(df)}")
print(f"Transfer required: {df['transfer_required'].sum()} ({df['transfer_required'].mean()*100:.1f}%)")
print(f"No transfer required: {(df['transfer_required'] == 0).sum()} ({(1-df['transfer_required'].mean())*100:.1f}%)")

# Preprocess the data
df = preprocess_data(df)

# Define features (including new engineered features)
feature_columns = [
    'current_quantity',
    'manufacturing_capacity_per_day',
    'lead_time_days',
    'stock_to_capacity_ratio',
    'production_potential',
    'stock_deficit',
    'urgency_score'
]

X = df[feature_columns]
y = df['transfer_required']

print(f"\nFeature matrix shape: {X.shape}")
print(f"Target vector shape: {y.shape}")

# Check if there is data to train on
if X.empty or y.empty:
    raise ValueError("The features (X) or labels (y) are empty. Please check the dataset.")

# Split the dataset into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(
    X, y,
    test_size=0.2,
    random_state=42,
    stratify=y  # Ensure balanced split
)

print(f"\nTraining set size: {X_train.shape[0]}")
print(f"Test set size: {X_test.shape[0]}")

# Check if there are enough samples
if X_train.shape[0] == 0 or X_test.shape[0] == 0:
    raise ValueError("Not enough samples in the dataset after splitting.")

# Train and evaluate models
best_model, best_name = train_and_evaluate_models(X_train, X_test, y_train, y_test)

# Ensure the directory exists
model_dir = 'inventory/ml_models'
os.makedirs(model_dir, exist_ok=True)

# Save the best model
model_path = os.path.join(model_dir, 'transfer_model.pkl')
joblib.dump(best_model, model_path)

print(f"\nBest model ({best_name}) saved to {model_path}")
print("Model training completed successfully!")
