import os
import django
import pandas as pd
import joblib
import random
import numpy as np
from datetime import datetime, timedelta

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "myproject.settings")
django.setup()

from inventory.models import Inventory, FactoryPart

def generate_synthetic_variations(base_record, num_variations=50):
    """Generate synthetic variations of a base record to create more training data"""
    variations = []

    for i in range(num_variations):
        # Create variations with realistic noise and scenarios
        variation = base_record.copy()

        # Add realistic variations to current_quantity (±20% with some outliers)
        base_qty = base_record['current_quantity']
        if random.random() < 0.1:  # 10% chance of extreme scenarios
            qty_multiplier = random.choice([0.1, 0.2, 2.0, 3.0])  # Very low or very high stock
        else:
            qty_multiplier = random.uniform(0.8, 1.2)  # Normal variation ±20%

        variation['current_quantity'] = max(0, int(base_qty * qty_multiplier))

        # Add variations to manufacturing capacity (±15%)
        base_capacity = base_record['manufacturing_capacity_per_day']
        capacity_multiplier = random.uniform(0.85, 1.15)
        variation['manufacturing_capacity_per_day'] = max(1, int(base_capacity * capacity_multiplier))

        # Add variations to lead time (±2 days)
        base_lead_time = base_record['lead_time_days']
        lead_time_variation = random.randint(-2, 2)
        variation['lead_time_days'] = max(1, base_lead_time + lead_time_variation)

        # Recalculate transfer_required based on new values
        stock_gap = variation['target_quantity'] - variation['current_quantity']
        production_capacity = variation['manufacturing_capacity_per_day'] * variation['lead_time_days']

        # Enhanced logic for transfer_required
        if stock_gap <= 0:
            # No transfer needed if stock meets or exceeds target
            transfer_required = 0
        elif stock_gap > production_capacity:
            # Definitely need transfer if gap exceeds production capacity
            transfer_required = 1
        elif stock_gap > production_capacity * 0.7:
            # Likely need transfer if gap is close to production capacity
            transfer_required = 1 if random.random() < 0.8 else 0
        elif stock_gap > production_capacity * 0.3:
            # Maybe need transfer for moderate gaps
            transfer_required = 1 if random.random() < 0.4 else 0
        else:
            # Unlikely to need transfer for small gaps
            transfer_required = 1 if random.random() < 0.1 else 0

        variation['transfer_required'] = transfer_required
        variations.append(variation)

    return variations

# Build the enhanced dataset
data = []
mappings = FactoryPart.objects.select_related('factory', 'part')

print(f"Found {mappings.count()} factory-part mappings. Generating enhanced dataset...")

for mapping in mappings:
    try:
        # Get current quantity from inventory or use 0 if not found
        inventory = Inventory.objects.get(factory=mapping.factory, part=mapping.part)
        current_quantity = inventory.current_quantity
    except Inventory.DoesNotExist:
        current_quantity = 0  # default if no inventory

    # Ensure we handle None or 0 for target_quantity
    target_quantity = mapping.target_quantity if mapping.target_quantity is not None else 0

    # Prepare the base record
    base_record = {
        'factory_id': mapping.factory.id,
        'part_id': mapping.part.id,
        'current_quantity': current_quantity,
        'manufacturing_capacity_per_day': mapping.manufacturing_capacity_per_day,
        'lead_time_days': mapping.lead_time_days,
        'target_quantity': target_quantity
    }

    # Calculate transfer_required for base record
    stock_gap = target_quantity - current_quantity
    production_capacity = mapping.manufacturing_capacity_per_day * mapping.lead_time_days
    base_record['transfer_required'] = 1 if stock_gap > production_capacity else 0

    # Add the original record
    data.append(base_record)

    # Generate synthetic variations (50 per original record)
    variations = generate_synthetic_variations(base_record, num_variations=50)
    data.extend(variations)

    if len(data) % 500 == 0:
        print(f"Generated {len(data)} records so far...")

print(f"Total records generated: {len(data)}")

# Create DataFrame and save to CSV
dataset_path = "inventory/ml_models/transfer_dataset.csv"
df = pd.DataFrame(data)

# Ensure the directory exists
os.makedirs(os.path.dirname(dataset_path), exist_ok=True)

# Add some statistics about the dataset
transfer_count = df['transfer_required'].sum()
total_count = len(df)
transfer_percentage = (transfer_count / total_count) * 100 if total_count > 0 else 0

print(f"\nDataset Statistics:")
print(f"Total records: {total_count}")
print(f"Transfer required: {transfer_count} ({transfer_percentage:.1f}%)")
print(f"No transfer required: {total_count - transfer_count} ({100 - transfer_percentage:.1f}%)")

# Shuffle the dataset to ensure good distribution
df = df.sample(frac=1, random_state=42).reset_index(drop=True)

# Save the dataset (overwrite existing file to avoid duplicates)
df.to_csv(dataset_path, index=False)

print(f"\nEnhanced dataset saved to {dataset_path}")
print("Dataset generation completed successfully!")
