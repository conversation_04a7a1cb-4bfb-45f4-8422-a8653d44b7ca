<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ action }} Audit Log - LogistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #003366;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-role {
            background-color: #ff9800;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 800px;
            margin: 40px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 51, 102, 0.1);
        }
        h2 {
            color: #003366;
            margin-bottom: 30px;
        }
        .form-section {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 10px 12px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #003366;
            box-shadow: 0 0 0 0.2rem rgba(0, 51, 102, 0.25);
        }
        .btn-primary {
            background-color: #003366;
            border-color: #003366;
            padding: 10px 20px;
        }
        .btn-primary:hover {
            background-color: #002244;
            border-color: #002244;
        }
        .required-field {
            color: #dc3545;
        }
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .form-errors {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>LogistiQ</h1>
        <div class="user-info">
            <span class="user-role">{{ user_role|upper }}</span>
            <div class="nav-links">
                <a href="{% url 'dashboard' %}">Dashboard</a>
                <a href="{% url 'audit_log_list' %}">Audit Logs</a>
                <a href="{% url 'user_management' %}">User Management</a>
                <a href="{% url 'logout' %}">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clipboard-list me-2"></i>{{ action }} Audit Log</h2>
            <a href="{% url 'audit_log_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>

        <!-- Form Errors -->
        {% if form.errors %}
            <div class="form-errors">
                <h6><i class="fas fa-exclamation-triangle me-1"></i>Please correct the following errors:</h6>
                <ul class="mb-0">
                    {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                            <li>{{ field|title }}: {{ error }}</li>
                        {% endfor %}
                    {% endfor %}
                </ul>
            </div>
        {% endif %}

        <form method="post">
            {% csrf_token %}
            
            <div class="form-section">
                <h4 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>Audit Information
                </h4>
                
                <!-- Factory Selection -->
                <div class="form-group">
                    <label for="{{ form.factory.id_for_label }}" class="form-label">
                        <i class="fas fa-industry me-1"></i>Factory <span class="required-field">*</span>
                    </label>
                    {{ form.factory }}
                    {% if form.factory.help_text %}
                        <div class="help-text">{{ form.factory.help_text }}</div>
                    {% endif %}
                    {% if form.factory.errors %}
                        <div class="text-danger mt-1">
                            {% for error in form.factory.errors %}
                                <small>{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Audit Notes -->
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                        <i class="fas fa-sticky-note me-1"></i>Audit Notes & Findings <span class="required-field">*</span>
                    </label>
                    {{ form.notes }}
                    {% if form.notes.help_text %}
                        <div class="help-text">{{ form.notes.help_text }}</div>
                    {% endif %}
                    {% if form.notes.errors %}
                        <div class="text-danger mt-1">
                            {% for error in form.notes.errors %}
                                <small>{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Current User Info (Read-only) -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user me-1"></i>Audited By
                    </label>
                    <input type="text" class="form-control" value="{{ user.username }}" readonly>
                    <div class="help-text">This audit will be recorded under your username.</div>
                </div>

                <!-- Current Date Info (Read-only) -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-calendar me-1"></i>Audit Date
                    </label>
                    <input type="text" class="form-control" value="{% now 'F j, Y, g:i a' %}" readonly>
                    <div class="help-text">The audit date will be automatically set to the current date and time.</div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between">
                <a href="{% url 'audit_log_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>{{ action }} Audit Log
                </button>
            </div>
        </form>

        <!-- Guidelines Section -->
        <div class="mt-4 p-3 bg-light border-start border-primary border-4">
            <h6><i class="fas fa-lightbulb me-1"></i>Audit Guidelines</h6>
            <ul class="mb-0 small">
                <li>Include detailed observations about factory operations, safety compliance, and quality standards</li>
                <li>Document any issues found, their severity, and recommended corrective actions</li>
                <li>Note positive findings and best practices observed during the audit</li>
                <li>Provide specific recommendations for improvement where applicable</li>
                <li>Include dates for follow-up actions if required</li>
            </ul>
        </div>
    </div>

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });

        // Auto-resize textarea
        const textarea = document.querySelector('textarea');
        if (textarea) {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        }
    </script>
</body>
</html>
