<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Activity Log - LogistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #003366;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-role {
            background-color: #ff9800;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 1000px;
            margin: 40px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 51, 102, 0.1);
        }
        h2 {
            text-align: center;
            color: #003366;
            margin-bottom: 30px;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .action-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        .action-create {
            background-color: #28a745;
            color: white;
        }
        .action-update {
            background-color: #007bff;
            color: white;
        }
        .action-delete {
            background-color: #dc3545;
            color: white;
        }
        .action-login {
            background-color: #6610f2;
            color: white;
        }
        .action-logout {
            background-color: #6c757d;
            color: white;
        }
        .action-view {
            background-color: #17a2b8;
            color: white;
        }
        .action-other {
            background-color: #fd7e14;
            color: white;
        }
        .timeline {
            position: relative;
            padding: 20px 0;
        }
        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 50px;
            width: 2px;
            background-color: #e9ecef;
        }
        .timeline-item {
            position: relative;
            padding-left: 80px;
            margin-bottom: 30px;
        }
        .timeline-badge {
            position: absolute;
            left: 30px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            color: white;
            z-index: 1;
        }
        .timeline-content {
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            background-color: white;
        }
        .timeline-date {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .pagination {
            justify-content: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>LogistiQ</h1>
        <div class="user-info">
            <span class="user-role">{{ user_role|upper }}</span>
            <div class="nav-links">
                <a href="{% url 'dashboard' %}">Dashboard</a>
                <a href="{% url 'user_management' %}">User Management</a>
                <a href="{% url 'logout' %}">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <h2>Activity Log</h2>
        
        <div class="timeline">
            {% for log in page_obj %}
                <div class="timeline-item">
                    {% if log.action == 'CREATE' %}
                        <div class="timeline-badge bg-success">
                            <i class="fas fa-plus"></i>
                        </div>
                    {% elif log.action == 'UPDATE' %}
                        <div class="timeline-badge bg-primary">
                            <i class="fas fa-edit"></i>
                        </div>
                    {% elif log.action == 'DELETE' %}
                        <div class="timeline-badge bg-danger">
                            <i class="fas fa-trash"></i>
                        </div>
                    {% elif log.action == 'LOGIN' %}
                        <div class="timeline-badge bg-purple">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                    {% elif log.action == 'LOGOUT' %}
                        <div class="timeline-badge bg-secondary">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                    {% elif log.action == 'VIEW' %}
                        <div class="timeline-badge bg-info">
                            <i class="fas fa-eye"></i>
                        </div>
                    {% else %}
                        <div class="timeline-badge bg-warning">
                            <i class="fas fa-cog"></i>
                        </div>
                    {% endif %}
                    
                    <div class="timeline-content">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">{{ log.user.username }}</span>
                            <span class="action-badge action-{{ log.action|lower }}">{{ log.get_action_display }}</span>
                        </div>
                        
                        <p class="mb-1">
                            {% if log.model_name %}
                                <strong>{{ log.model_name }}</strong>
                                {% if log.object_repr %} - {{ log.object_repr }}{% endif %}
                            {% else %}
                                <em>No specific model affected</em>
                            {% endif %}
                        </p>
                        
                        {% if log.details %}
                            <p class="small text-muted mb-1">{{ log.details }}</p>
                        {% endif %}
                        
                        <div class="timeline-date">
                            <i class="far fa-clock me-1"></i> {{ log.timestamp|date:"F j, Y, g:i a" }}
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="text-center py-5">
                    <p class="text-muted">No activity logs found.</p>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <nav aria-label="Activity log pagination">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    </div>

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
