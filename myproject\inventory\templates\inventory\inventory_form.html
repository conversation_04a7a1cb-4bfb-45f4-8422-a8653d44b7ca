{% extends 'inventory/base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>{% if form.instance.pk %}Edit{% else %}Add{% endif %} Inventory</h2>
    <form method="post" novalidate>
        {% csrf_token %}
        {{ form.as_p }}
        <button type="submit" class="btn btn-success mt-2">Save</button>
        <a href="{% url 'inventory_list' %}" class="btn btn-secondary mt-2">Cancel</a>
    </form>
</div>
{% endblock %}
