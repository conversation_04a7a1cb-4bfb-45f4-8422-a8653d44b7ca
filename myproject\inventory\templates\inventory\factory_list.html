<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Factory List | LogistiQ</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f4f9ff;
    }
    .btn-rounded {
      border-radius: 20px;
    }
    .table thead {
      background-color: #e0f0ff;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>

  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
    <div class="container-fluid">
      <a class="navbar-brand fw-bold text-primary" href="#">LogistiQ</a>
      <div class="d-flex">
        <form action="{% url 'logout' %}" method="post" class="d-inline">
          {% csrf_token %}
          <button type="submit" class="btn btn-outline-danger btn-rounded">Logout</button>
        </form>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="container mt-5">
    <div class="header">
      <h2 class="text-primary">Factory List</h2>
      <a href="{% url 'factory_create' %}" class="btn btn-primary btn-rounded">+ Add Factory</a>
    </div>

    <!-- Home Button -->
    <div class="mb-4">
      <a href="{% url 'dashboard' %}" class="btn btn-outline-info btn-rounded">🏠 Home</a>
    </div>
    
    <table class="table table-bordered shadow-sm bg-white rounded">
      <thead>
        <tr>
          <th>ID</th>
          <th>Name</th>
          <th>Location</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for factory in factories %}
        <tr>
          <td>{{ factory.id }}</td>
          <td>{{ factory.name }}</td>
          <td>{{ factory.location }}</td>
          <td>
            <a href="{% url 'factory_update' factory.id %}" class="btn btn-sm btn-outline-primary btn-rounded">Edit</a>
            <form action="{% url 'factory_delete' factory.id %}" method="post" style="display:inline;">
              {% csrf_token %}
              <button type="submit" class="btn btn-sm btn-outline-danger btn-rounded">Delete</button>
            </form>
          </td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="4" class="text-center text-muted">No factories available.</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>

</body>
</html>
