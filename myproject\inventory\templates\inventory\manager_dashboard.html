<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Manager Dashboard - LogistiQ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f9ff;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #2c7be5;
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header h1 {
            margin: 0;
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-role {
            background-color: #4caf50;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
        }
        .container {
            max-width: 1000px;
            margin: 40px auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(44, 123, 229, 0.1);
        }
        h2 {
            text-align: center;
            color: #2c7be5;
            margin-bottom: 30px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .dashboard-item {
            background-color: #e9f3ff;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .dashboard-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(44, 123, 229, 0.1);
        }
        .dashboard-item a {
            display: block;
            color: #2c7be5;
            text-decoration: none;
            font-weight: 500;
        }
        .dashboard-item i {
            display: block;
            font-size: 2rem;
            margin-bottom: 10px;
            color: #2c7be5;
        }
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: fadeIn 0.5s;
            max-width: 400px;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Success notification -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} success-notification">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <header>
        <h1>LogistiQ</h1>
        <div class="user-info">
            <span class="user-role">MANAGER</span>
            <div class="nav-links">
                <a href="{% url 'dashboard' %}">Home</a>
                <a href="{% url 'logout' %}">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <h2>Manager Dashboard</h2>
        <div class="dashboard-grid">
            <div class="dashboard-item">
                <i class="fas fa-industry"></i>
                <a href="{% url 'factory_list' %}">Factory Management</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-cogs"></i>
                <a href="{% url 'part_list' %}">Part Management</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-link"></i>
                <a href="{% url 'factory_part_list' %}">Factory-Part Mapping</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-boxes"></i>
                <a href="{% url 'inventory_list' %}">Inventory Management</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-chart-line"></i>
                <a href="{% url 'production_log_dashboard' %}">Production Logs</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-clipboard-list"></i>
                <a href="{% url 'audit_log_list' %}">Audit Logs</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-users"></i>
                <a href="{% url 'user_management' %}">User Management</a>
            </div>
            <div class="dashboard-item">
                <i class="fas fa-history"></i>
                <a href="{% url 'activity_log' %}">Activity Logs</a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS for notifications auto-close -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-close notifications after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                var alerts = document.querySelectorAll('.success-notification');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.style.display = 'none';
                    }, 500);
                });
            }, 5000);
        });
    </script>
</body>
</html>
